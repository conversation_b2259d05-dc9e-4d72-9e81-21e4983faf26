import React from 'react';
import Header from './components/header/Header';
import Hero from './components/hero/Hero';
import About from './components/about/About';
import Projects from './components/projects/Projects';
import Contact from './components/contact/Contact';
import Footer from './components/footer/Footer';
import './App.css';

function App() {
  return (
    <div className="App">
      <Header />
      <main>
        <Hero />
        <About />
        <Projects />
        <Contact />
      </main>
      <Footer />
    </div>
  );
}

export default App;
        <nav className="container-custom">
          <div className="header-nav">
            <div className="header-logo font-display gradient-text" onClick={() => scrollToSection('#home')}>
              Portfolio
            </div>

            <div className="header-desktop-nav">
              <button onClick={() => scrollToSection('#home')} className="header-nav-button">Home</button>
              <button onClick={() => scrollToSection('#about')} className="header-nav-button">About</button>
              <button onClick={() => scrollToSection('#projects')} className="header-nav-button">Projects</button>
              <button onClick={() => scrollToSection('#contact')} className="header-nav-button">Contact</button>
              <button onClick={() => scrollToSection('#contact')} className="btn-primary">Get In Touch</button>
            </div>

            <button
              className={`header-mobile-button ${isMenuOpen ? 'open' : ''}`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <div className="hamburger">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>
          </div>

          <div className={`header-mobile-nav ${isMenuOpen ? 'open' : ''}`}>
            <div className="header-mobile-nav-content">
              <button onClick={() => scrollToSection('#home')} className="header-mobile-nav-button">Home</button>
              <button onClick={() => scrollToSection('#about')} className="header-mobile-nav-button">About</button>
              <button onClick={() => scrollToSection('#projects')} className="header-mobile-nav-button">Projects</button>
              <button onClick={() => scrollToSection('#contact')} className="header-mobile-nav-button">Contact</button>
              <button onClick={() => scrollToSection('#contact')} className="btn-primary header-mobile-cta">Get In Touch</button>
            </div>
          </div>
        </nav>

        {/* Mobile Menu Backdrop */}
        {isMenuOpen && (
          <div
            className="mobile-backdrop"
            onClick={() => setIsMenuOpen(false)}
          />
        )}
      </header>

      <main>
        {/* Hero Section */}
        <section id="home" className="hero-section">
          <div className="hero-background">
            <div className="hero-bg-element hero-bg-1"></div>
            <div className="hero-bg-element hero-bg-2"></div>
          </div>

          <div className="container-custom hero-content">
            <div className="hero-text">
              <div className="hero-greeting">Hello, I'm</div>
              <h1 className="hero-name font-display gradient-text">Your Name</h1>
              <h2 className="hero-title font-display">Full Stack Developer</h2>
              <p className="hero-description">
                I create beautiful, responsive web applications with modern technologies.
                Passionate about clean code, user experience, and bringing ideas to life.
              </p>

              <div className="hero-buttons">
                <button onClick={() => scrollToSection('#projects')} className="btn-primary">
                  View My Work
                </button>
                <button className="btn-secondary">
                  📄 Download CV
                </button>
              </div>

              <div className="hero-social">
                <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="social-link">GitHub</a>
                <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="social-link">LinkedIn</a>
                <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="social-link">Twitter</a>
              </div>
            </div>

            <div className="scroll-indicator">
              <div className="scroll-mouse">
                <div className="scroll-wheel"></div>
              </div>
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about" className="about-section">
          <div className="container-custom">
            <div className="about-grid">
              <div className="about-text">
                <h2 className="section-title font-display">
                  About <span className="gradient-text">Me</span>
                </h2>
                <div className="about-description">
                  <p>
                    I'm a passionate full-stack developer with over 3 years of experience
                    creating digital solutions that make a difference. I love turning complex
                    problems into simple, beautiful, and intuitive designs.
                  </p>
                  <p>
                    When I'm not coding, you'll find me exploring new technologies,
                    contributing to open-source projects, or sharing my knowledge with
                    the developer community.
                  </p>
                </div>

                <div className="about-stats">
                  <div className="stat">
                    <div className="stat-number gradient-text">50+</div>
                    <div className="stat-label">Projects Completed</div>
                  </div>
                  <div className="stat">
                    <div className="stat-number gradient-text">3+</div>
                    <div className="stat-label">Years Experience</div>
                  </div>
                  <div className="stat">
                    <div className="stat-number gradient-text">100%</div>
                    <div className="stat-label">Client Satisfaction</div>
                  </div>
                  <div className="stat">
                    <div className="stat-number gradient-text">24/7</div>
                    <div className="stat-label">Support</div>
                  </div>
                </div>

                <button onClick={() => scrollToSection('#contact')} className="btn-primary">
                  Let's Work Together
                </button>
              </div>

              <div className="about-skills">
                <h3 className="skills-title font-display">
                  Skills & <span className="gradient-text">Technologies</span>
                </h3>
                <div className="skills-grid">
                  <div className="skill-item">
                    <div className="skill-icon">⚛️</div>
                    <span>React</span>
                  </div>
                  <div className="skill-item">
                    <div className="skill-icon">📗</div>
                    <span>Node.js</span>
                  </div>
                  <div className="skill-item">
                    <div className="skill-icon">📜</div>
                    <span>JavaScript</span>
                  </div>
                  <div className="skill-item">
                    <div className="skill-icon">🔷</div>
                    <span>TypeScript</span>
                  </div>
                  <div className="skill-item">
                    <div className="skill-icon">🐍</div>
                    <span>Python</span>
                  </div>
                  <div className="skill-item">
                    <div className="skill-icon">🍃</div>
                    <span>MongoDB</span>
                  </div>
                  <div className="skill-item">
                    <div className="skill-icon">🐘</div>
                    <span>PostgreSQL</span>
                  </div>
                  <div className="skill-item">
                    <div className="skill-icon">🎨</div>
                    <span>CSS</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Projects Section */}
        <section id="projects" className="projects-section">
          <div className="container-custom">
            <div className="section-header">
              <h2 className="section-title font-display">
                Featured <span className="gradient-text">Projects</span>
              </h2>
              <p className="section-description">
                Here are some of my recent projects that showcase my skills and passion for creating
                innovative digital solutions.
              </p>
            </div>

            <div className="projects-grid">
              <div className="project-card featured">
                <div className="project-image">
                  <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="E-Commerce Platform" />
                  <div className="project-overlay">
                    <div className="project-links">
                      <a href="#" className="project-link">🔗</a>
                      <a href="#" className="project-link">📁</a>
                    </div>
                  </div>
                </div>
                <div className="project-content">
                  <h3 className="project-title">E-Commerce Platform</h3>
                  <p className="project-description">
                    A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment integration, and admin dashboard.
                  </p>
                  <div className="project-tech">
                    <span className="tech-tag">React</span>
                    <span className="tech-tag">Node.js</span>
                    <span className="tech-tag">MongoDB</span>
                    <span className="tech-tag">Stripe</span>
                  </div>
                </div>
              </div>

              <div className="project-card featured">
                <div className="project-image">
                  <img src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Task Management App" />
                  <div className="project-overlay">
                    <div className="project-links">
                      <a href="#" className="project-link">🔗</a>
                      <a href="#" className="project-link">📁</a>
                    </div>
                  </div>
                </div>
                <div className="project-content">
                  <h3 className="project-title">Task Management App</h3>
                  <p className="project-description">
                    A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.
                  </p>
                  <div className="project-tech">
                    <span className="tech-tag">React</span>
                    <span className="tech-tag">TypeScript</span>
                    <span className="tech-tag">Socket.io</span>
                    <span className="tech-tag">PostgreSQL</span>
                  </div>
                </div>
              </div>

              <div className="project-card">
                <div className="project-image">
                  <img src="https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Weather Dashboard" />
                  <div className="project-overlay">
                    <div className="project-links">
                      <a href="#" className="project-link">🔗</a>
                      <a href="#" className="project-link">📁</a>
                    </div>
                  </div>
                </div>
                <div className="project-content">
                  <h3 className="project-title">Weather Dashboard</h3>
                  <p className="project-description">
                    A responsive weather application with location-based forecasts, interactive maps, and detailed weather analytics.
                  </p>
                  <div className="project-tech">
                    <span className="tech-tag">React</span>
                    <span className="tech-tag">API Integration</span>
                    <span className="tech-tag">Chart.js</span>
                  </div>
                </div>
              </div>

              <div className="project-card">
                <div className="project-image">
                  <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Social Media Dashboard" />
                  <div className="project-overlay">
                    <div className="project-links">
                      <a href="#" className="project-link">🔗</a>
                      <a href="#" className="project-link">📁</a>
                    </div>
                  </div>
                </div>
                <div className="project-content">
                  <h3 className="project-title">Social Media Dashboard</h3>
                  <p className="project-description">
                    Analytics dashboard for social media management with data visualization, scheduling, and performance tracking.
                  </p>
                  <div className="project-tech">
                    <span className="tech-tag">Vue.js</span>
                    <span className="tech-tag">Python</span>
                    <span className="tech-tag">Django</span>
                  </div>
                </div>
              </div>

              <div className="project-card">
                <div className="project-image">
                  <img src="https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Portfolio Website" />
                  <div className="project-overlay">
                    <div className="project-links">
                      <a href="#" className="project-link">🔗</a>
                      <a href="#" className="project-link">📁</a>
                    </div>
                  </div>
                </div>
                <div className="project-content">
                  <h3 className="project-title">Portfolio Website</h3>
                  <p className="project-description">
                    A modern, responsive portfolio website built with React, featuring smooth animations and mobile-first design.
                  </p>
                  <div className="project-tech">
                    <span className="tech-tag">React</span>
                    <span className="tech-tag">CSS3</span>
                    <span className="tech-tag">Responsive</span>
                  </div>
                </div>
              </div>

              <div className="project-card">
                <div className="project-image">
                  <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="Fitness Tracker" />
                  <div className="project-overlay">
                    <div className="project-links">
                      <a href="#" className="project-link">🔗</a>
                      <a href="#" className="project-link">📁</a>
                    </div>
                  </div>
                </div>
                <div className="project-content">
                  <h3 className="project-title">Fitness Tracker</h3>
                  <p className="project-description">
                    Mobile-responsive fitness tracking application with workout plans, progress tracking, and social features.
                  </p>
                  <div className="project-tech">
                    <span className="tech-tag">React Native</span>
                    <span className="tech-tag">Firebase</span>
                    <span className="tech-tag">Redux</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="section-cta">
              <button className="btn-secondary">
                🔍 View All Projects
              </button>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="contact-section">
          <div className="container-custom">
            <div className="section-header">
              <h2 className="section-title font-display">
                Get In <span className="gradient-text">Touch</span>
              </h2>
              <p className="section-description">
                Have a project in mind or just want to chat? I'd love to hear from you.
                Let's create something amazing together!
              </p>
            </div>

            <div className="contact-grid">
              <div className="contact-info">
                <h3 className="contact-title font-display">
                  Let's <span className="gradient-text">Connect</span>
                </h3>

                <div className="contact-items">
                  <a href="mailto:<EMAIL>" className="contact-item">
                    <div className="contact-icon">📧</div>
                    <div className="contact-details">
                      <h4>Email</h4>
                      <p><EMAIL></p>
                    </div>
                  </a>

                  <a href="tel:+15551234567" className="contact-item">
                    <div className="contact-icon">📞</div>
                    <div className="contact-details">
                      <h4>Phone</h4>
                      <p>+****************</p>
                    </div>
                  </a>

                  <div className="contact-item">
                    <div className="contact-icon">📍</div>
                    <div className="contact-details">
                      <h4>Location</h4>
                      <p>New York, NY</p>
                    </div>
                  </div>
                </div>

                <div className="contact-social">
                  <h4>Follow Me</h4>
                  <div className="social-links">
                    <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="social-icon">GitHub</a>
                    <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="social-icon">LinkedIn</a>
                    <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="social-icon">Twitter</a>
                    <a href="https://dribbble.com" target="_blank" rel="noopener noreferrer" className="social-icon">Dribbble</a>
                  </div>
                </div>
              </div>

              <form className="contact-form">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="name">Name</label>
                    <input type="text" id="name" name="name" required placeholder="Your Name" />
                  </div>
                  <div className="form-group">
                    <label htmlFor="email">Email</label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>" />
                  </div>
                </div>
                <div className="form-group">
                  <label htmlFor="subject">Subject</label>
                  <input type="text" id="subject" name="subject" required placeholder="Project Discussion" />
                </div>
                <div className="form-group">
                  <label htmlFor="message">Message</label>
                  <textarea id="message" name="message" required rows="6" placeholder="Tell me about your project..."></textarea>
                </div>
                <button type="submit" className="btn-primary form-submit">
                  ✈️ Send Message
                </button>
              </form>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="footer">
        <div className="container-custom">
          <div className="footer-content">
            <div className="footer-text">
              <span>© 2024 Your Name. Made with ❤️ and lots of coffee.</span>
            </div>
            <button onClick={() => scrollToSection('#home')} className="back-to-top">
              <span>Back to Top</span>
              <div className="back-to-top-icon">↑</div>
            </button>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
