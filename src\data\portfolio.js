// Personal Information
export const personalInfo = {
  name: "Your Name",
  title: "Full Stack Developer",
  greeting: "Hello, I'm",
  description: "I create beautiful, responsive web applications with modern technologies. Passionate about clean code, user experience, and bringing ideas to life.",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, NY",
  resumeUrl: "#", // Add your resume URL here
};

// Navigation Items
export const navigationItems = [
  { name: 'Home', href: '#home' },
  { name: 'About', href: '#about' },
  { name: 'Projects', href: '#projects' },
  { name: 'Contact', href: '#contact' },
];

// Social Links
export const socialLinks = [
  { 
    name: 'GitHub', 
    url: 'https://github.com', 
    icon: '🔗',
    color: '#333' 
  },
  { 
    name: 'LinkedIn', 
    url: 'https://linkedin.com', 
    icon: '💼',
    color: '#0077B5' 
  },
  { 
    name: 'Twitter', 
    url: 'https://twitter.com', 
    icon: '🐦',
    color: '#1DA1F2' 
  },
  { 
    name: 'Dribbble', 
    url: 'https://dribbble.com', 
    icon: '🎨',
    color: '#EA4C89' 
  },
];

// Skills and Technologies
export const skills = [
  { name: 'React', icon: '⚛️', color: '#61DAFB' },
  { name: 'Node.js', icon: '📗', color: '#339933' },
  { name: 'JavaScript', icon: '📜', color: '#F7DF1E' },
  { name: 'TypeScript', icon: '🔷', color: '#3178C6' },
  { name: 'Python', icon: '🐍', color: '#3776AB' },
  { name: 'MongoDB', icon: '🍃', color: '#47A248' },
  { name: 'PostgreSQL', icon: '🐘', color: '#336791' },
  { name: 'CSS3', icon: '🎨', color: '#1572B6' },
  { name: 'Git', icon: '📚', color: '#F05032' },
  { name: 'Docker', icon: '🐳', color: '#2496ED' },
];

// Statistics
export const stats = [
  { number: '50+', label: 'Projects Completed' },
  { number: '3+', label: 'Years Experience' },
  { number: '100%', label: 'Client Satisfaction' },
  { number: '24/7', label: 'Support' },
];

// About Information
export const aboutInfo = {
  title: "About Me",
  description: [
    "I'm a passionate full-stack developer with over 3 years of experience creating digital solutions that make a difference. I love turning complex problems into simple, beautiful, and intuitive designs.",
    "When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or sharing my knowledge with the developer community."
  ],
  skillsTitle: "Skills & Technologies",
};

// Projects Data
export const projects = [
  {
    id: 1,
    title: 'E-Commerce Platform',
    description: 'A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment integration, and admin dashboard.',
    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
    liveUrl: '#',
    githubUrl: '#',
    featured: true
  },
  {
    id: 2,
    title: 'Task Management App',
    description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    technologies: ['React', 'TypeScript', 'Socket.io', 'PostgreSQL'],
    liveUrl: '#',
    githubUrl: '#',
    featured: true
  },
  {
    id: 3,
    title: 'Weather Dashboard',
    description: 'A responsive weather application with location-based forecasts, interactive maps, and detailed weather analytics.',
    image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    technologies: ['React', 'API Integration', 'Chart.js', 'CSS3'],
    liveUrl: '#',
    githubUrl: '#',
    featured: false
  },
  {
    id: 4,
    title: 'Social Media Dashboard',
    description: 'Analytics dashboard for social media management with data visualization, scheduling, and performance tracking.',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    technologies: ['Vue.js', 'Python', 'Django', 'D3.js'],
    liveUrl: '#',
    githubUrl: '#',
    featured: false
  },
  {
    id: 5,
    title: 'Portfolio Website',
    description: 'A modern, responsive portfolio website built with React, featuring smooth animations and mobile-first design.',
    image: 'https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    technologies: ['React', 'CSS3', 'Responsive Design'],
    liveUrl: '#',
    githubUrl: '#',
    featured: false
  },
  {
    id: 6,
    title: 'Fitness Tracker',
    description: 'Mobile-responsive fitness tracking application with workout plans, progress tracking, and social features.',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    technologies: ['React Native', 'Firebase', 'Redux', 'Chart.js'],
    liveUrl: '#',
    githubUrl: '#',
    featured: false
  }
];

// Contact Information
export const contactInfo = {
  title: "Get In Touch",
  subtitle: "Let's Connect",
  description: "Have a project in mind or just want to chat? I'd love to hear from you. Let's create something amazing together!",
  items: [
    {
      icon: '📧',
      title: 'Email',
      value: personalInfo.email,
      href: `mailto:${personalInfo.email}`
    },
    {
      icon: '📞',
      title: 'Phone',
      value: personalInfo.phone,
      href: `tel:${personalInfo.phone.replace(/\s/g, '')}`
    },
    {
      icon: '📍',
      title: 'Location',
      value: personalInfo.location,
      href: '#'
    }
  ]
};

// Footer Information
export const footerInfo = {
  copyright: `© ${new Date().getFullYear()} ${personalInfo.name}. Made with ❤️ and lots of coffee.`,
  backToTopText: "Back to Top"
};
