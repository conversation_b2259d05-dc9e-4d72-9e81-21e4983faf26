import React from 'react';
import './Grid.css';

const Grid = ({ 
  children, 
  cols = 1,
  gap = 'medium',
  responsive = true,
  className = '',
  ...props 
}) => {
  const baseClasses = `grid grid-cols-${cols} grid-gap-${gap}`;
  const responsiveClass = responsive ? 'grid-responsive' : '';
  const classes = `${baseClasses} ${responsiveClass} ${className}`.trim();

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};

const GridItem = ({ 
  children, 
  span = 1,
  className = '',
  ...props 
}) => {
  const classes = `grid-item grid-span-${span} ${className}`.trim();

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};

Grid.Item = GridItem;

export default Grid;
