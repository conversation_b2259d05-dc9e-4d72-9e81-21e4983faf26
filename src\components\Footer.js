import React from 'react';
import { motion } from 'framer-motion';
import { FaHeart } from 'react-icons/fa';
import { HiArrowUp } from 'react-icons/hi';

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-secondary-900 border-t border-secondary-800">
      <div className="container-custom py-8">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          {/* Copyright */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex items-center gap-2 text-secondary-400"
          >
            <span>© {currentYear} Your Name. Made with</span>
            <FaHeart className="text-red-500 animate-pulse" size={16} />
            <span>and lots of coffee.</span>
          </motion.div>

          {/* Back to Top Button */}
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            onClick={scrollToTop}
            className="flex items-center gap-2 text-secondary-400 hover:text-primary-400 transition-colors duration-300 group"
          >
            <span className="text-sm font-medium">Back to Top</span>
            <div className="w-8 h-8 bg-secondary-800 rounded-full flex items-center justify-center group-hover:bg-primary-600 transition-colors duration-300">
              <HiArrowUp size={16} className="group-hover:text-white" />
            </div>
          </motion.button>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
