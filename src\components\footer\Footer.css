/* Footer */
.footer {
  background-color: var(--secondary-900);
  border-top: 1px solid var(--secondary-800);
  padding: 2rem 0;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

@media (min-width: 768px) {
  .footer-content {
    flex-direction: row;
  }
}

.footer-text {
  color: var(--secondary-400);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
}

@media (min-width: 768px) {
  .footer-text {
    text-align: left;
  }
}

.back-to-top {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--secondary-400);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.back-to-top:hover {
  color: var(--primary-400);
  transform: translateY(-2px);
}

.back-to-top-icon {
  width: 2rem;
  height: 2rem;
  background-color: var(--secondary-800);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-to-top:hover .back-to-top-icon {
  background-color: var(--primary-600);
  color: white;
}
