/* Contact Section */
.contact-grid {
  align-items: flex-start;
}

.contact-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .contact-title {
    font-size: 1.875rem;
  }
}

.contact-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: rgba(51, 65, 85, 0.5);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.contact-item:hover {
  background-color: var(--secondary-700);
  transform: translateX(10px);
}

.contact-icon {
  width: 3rem;
  height: 3rem;
  background-color: var(--primary-600);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: background-color 0.3s ease;
}

.contact-item:hover .contact-icon {
  background-color: var(--primary-700);
}

.contact-details h4 {
  font-weight: 600;
  color: var(--secondary-200);
  margin-bottom: 0.25rem;
}

.contact-details p {
  color: var(--secondary-400);
  margin: 0;
  transition: color 0.3s ease;
}

.contact-item:hover .contact-details p {
  color: var(--secondary-300);
}

.contact-social h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-icon {
  width: 3rem;
  height: 3rem;
  background-color: var(--secondary-700);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--secondary-400);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background-color: var(--secondary-600);
  color: var(--primary-400);
  transform: translateY(-2px);
}

/* Contact Form */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--secondary-300);
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--secondary-700);
  border: 1px solid var(--secondary-600);
  border-radius: 0.5rem;
  color: var(--secondary-200);
  font-size: 1rem;
  transition: all 0.3s ease;
  resize: none;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--secondary-400);
}

.form-submit {
  width: 100%;
  justify-content: center;
}

/* Mobile responsive */
@media (max-width: 767px) {
  .contact-grid {
    gap: 2rem;
  }
  
  .contact-items {
    gap: 1rem;
  }
  
  .contact-item {
    padding: 0.75rem;
  }
  
  .contact-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .social-links {
    gap: 0.75rem;
  }
  
  .social-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 0.75rem;
  }
}
