import React from 'react';
import { But<PERSON> } from '../ui';
import SocialLinks from './SocialLinks';

const HeroContent = ({ 
  greeting,
  name, 
  title, 
  description, 
  socialLinks,
  onViewWork,
  onDownloadCV,
  resumeUrl="public/aa.png"
}) => {
  return (
    <div className="hero-content">
      <div className="hero-text">
        <div className="hero-greeting">{greeting}</div>
        <h1 className="hero-name font-display gradient-text">{name}</h1>
        <h2 className="hero-title font-display">{title}</h2>
        <p className="hero-description">{description}</p>
        
        <div className="hero-buttons">
          <Button onClick={onViewWork}>
            View My Work
          </Button>
          <Button 
            variant="secondary" 
            href={resumeUrl}
            icon="📄"
          >
            Download CV
          </Button>
        </div>
        
        <SocialLinks links={socialLinks} />
      </div>
    </div>
  );
};

export default HeroContent;
