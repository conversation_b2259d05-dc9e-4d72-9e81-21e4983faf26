/* About Section */
.about-grid {
  align-items: center;
}

.about-text {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.about-description {
  color: var(--secondary-300);
  font-size: 1.125rem;
  line-height: 1.7;
}

.about-description p {
  margin-bottom: 1rem;
}

.about-description p:last-child {
  margin-bottom: 0;
}

.about-stats {
  margin: 2rem 0;
}

.stat {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
  .stat-number {
    font-size: 1.875rem;
  }
}

.stat-label {
  color: var(--secondary-400);
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .stat-label {
    font-size: 1rem;
  }
}

.skills-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
}

@media (min-width: 768px) {
  .skills-title {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .skills-title {
    text-align: left;
  }
}

.skills-grid {
  margin-top: 2rem;
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.skill-item:hover {
  transform: translateY(-5px) scale(1.1);
}

.skill-icon {
  width: 4rem;
  height: 4rem;
  background-color: var(--secondary-700);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  font-size: 2rem;
  transition: background-color 0.3s ease;
}

.skill-item:hover .skill-icon {
  background-color: var(--secondary-600);
}

.skill-item span {
  font-size: 0.875rem;
  color: var(--secondary-400);
  text-align: center;
  transition: color 0.3s ease;
}

.skill-item:hover span {
  color: var(--secondary-300);
}

/* Mobile responsive */
@media (max-width: 767px) {
  .about-grid {
    gap: 2.5rem;
  }
  
  .about-text {
    gap: 1.5rem;
  }
  
  .about-description {
    font-size: 1rem;
  }
  
  .about-stats {
    margin: 1.5rem 0;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .skills-grid {
    margin-top: 1.5rem;
  }
  
  .skill-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
  }
  
  .skill-item span {
    font-size: 0.75rem;
  }
}
