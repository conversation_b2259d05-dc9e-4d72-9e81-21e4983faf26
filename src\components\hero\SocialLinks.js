import React from 'react';

const SocialLinks = ({ links, className = '' }) => {
  return (
    <div className={`hero-social ${className}`}>
      {links.map((link) => (
        <a
          key={link.name}
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          className="social-link"
          aria-label={link.name}
        >
          {link.name}
        </a>
      ))}
    </div>
  );
};

export default SocialLinks;
