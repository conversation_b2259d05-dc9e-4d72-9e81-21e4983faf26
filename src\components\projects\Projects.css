/* Projects Section */
.projects-grid {
  margin-bottom: 3rem;
}

.project-card {
  cursor: pointer;
}

.project-card.featured {
  grid-column: span 1;
}

@media (min-width: 768px) {
  .project-card.featured {
    grid-column: span 2;
  }
}

.project-image {
  position: relative;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 12rem;
  object-fit: cover;
  transition: transform 0.5s ease;
}

@media (min-width: 768px) {
  .project-image img {
    height: 16rem;
  }
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(15, 23, 42, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 1rem;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-links {
  display: flex;
  gap: 0.5rem;
}

.project-link {
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--primary-600);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 1.125rem;
  transition: all 0.3s ease;
}

.project-link:hover {
  background-color: var(--primary-700);
  transform: scale(1.1);
}

.project-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  transition: color 0.3s ease;
}

@media (min-width: 768px) {
  .project-title {
    font-size: 1.5rem;
  }
}

.project-card:hover .project-title {
  color: var(--primary-400);
}

.project-description {
  color: var(--secondary-400);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  padding: 0.25rem 0.75rem;
  background-color: rgba(37, 99, 235, 0.2);
  color: var(--primary-400);
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.section-cta {
  text-align: center;
}

/* Mobile responsive */
@media (max-width: 767px) {
  .projects-grid {
    margin-bottom: 2rem;
  }
  
  .project-card.featured {
    grid-column: span 1;
  }
  
  .project-image img {
    height: 10rem;
  }
  
  .project-title {
    font-size: 1.125rem;
  }
  
  .project-description {
    font-size: 0.875rem;
  }
}
