.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  transition: all 0.3s ease;
}

.header.scrolled {
  background-color: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(12px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4.5rem;
  position: relative;
  padding: 0 1.5rem;
}

@media (min-width: 768px) {
  .header-nav {
    height: 5rem;
    padding: 0;
  }
}

.header-logo {
  font-size: 1.25rem;
  font-weight: 700;
  cursor: pointer;
}

@media (min-width: 768px) {
  .header-logo {
    font-size: 1.5rem;
  }
}

.header-desktop-nav {
  display: none;
  align-items: center;
  gap: 2rem;
}

@media (min-width: 768px) {
  .header-desktop-nav {
    display: flex;
  }
}

.header-nav-button {
  background: none;
  border: none;
  color: var(--secondary-300);
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.header-nav-button:hover {
  color: var(--primary-400);
}

.header-mobile-button {
  display: block;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.75rem;
  margin-right: -0.5rem;
  transition: all 0.3s ease;
}

@media (min-width: 768px) {
  .header-mobile-button {
    display: none;
  }
}

.hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--secondary-300);
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.header-mobile-button:hover .hamburger span {
  background-color: var(--primary-400);
}

.header-mobile-button.open .hamburger span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.header-mobile-button.open .hamburger span:nth-child(2) {
  opacity: 0;
}

.header-mobile-button.open .hamburger span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.header-mobile-nav {
  background-color: rgba(15, 23, 42, 0.98);
  backdrop-filter: blur(12px);
  border-top: 1px solid var(--secondary-700);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 40;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-mobile-nav.open {
  max-height: 450px;
  opacity: 1;
}

@media (min-width: 768px) {
  .header-mobile-nav {
    display: none !important;
  }
}

.header-mobile-nav-content {
  padding: 2rem 0 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.header-mobile-nav-button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 1.25rem 2rem;
  background: none;
  border: none;
  color: var(--secondary-200);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.125rem;
  font-weight: 500;
  border-bottom: 1px solid rgba(203, 213, 225, 0.1);
}

.header-mobile-nav-button:hover {
  color: var(--primary-400);
  background-color: rgba(37, 99, 235, 0.1);
  transform: translateX(8px);
}

.header-mobile-nav-button:last-of-type {
  border-bottom: none;
  margin-bottom: 1rem;
}

.header-mobile-cta {
  margin: 1rem 2rem 1rem;
  width: calc(100% - 4rem);
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
}

/* Mobile Backdrop */
.mobile-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 30;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@media (min-width: 768px) {
  .mobile-backdrop {
    display: none;
  }
}
