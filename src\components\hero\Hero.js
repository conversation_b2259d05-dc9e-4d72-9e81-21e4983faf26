import React from 'react';
import { Section } from '../ui';
import HeroB<PERSON>ground from './HeroBackground';
import HeroContent from './HeroContent';
import ScrollIndicator from './ScrollIndicator';
import { personalInfo, socialLinks } from '../../data/portfolio';
import './Hero.css';

const Hero = () => {
  const scrollToSection = (href) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleViewWork = () => {
    scrollToSection('#projects');
  };

  return (
    <Section 
      id="home" 
      className="hero-section"
      padding="none"
    >
      <HeroBackground />
      
      <HeroContent 
        greeting={personalInfo.greeting}
        name={personalInfo.name}
        title={personalInfo.title}
        description={personalInfo.description}
        socialLinks={socialLinks}
        onViewWork={handleViewWork}
        resumeUrl={personalInfo.resumeUrl}
      />
      
      <ScrollIndicator />
    </Section>
  );
};

export default Hero;
