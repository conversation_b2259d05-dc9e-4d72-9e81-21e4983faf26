.grid {
  display: grid;
  width: 100%;
}

/* Grid columns */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

/* Grid gaps */
.grid-gap-none { gap: 0; }
.grid-gap-small { gap: 1rem; }
.grid-gap-medium { gap: 1.5rem; }
.grid-gap-large { gap: 2rem; }
.grid-gap-xl { gap: 3rem; }

/* Grid item spans */
.grid-span-1 { grid-column: span 1; }
.grid-span-2 { grid-column: span 2; }
.grid-span-3 { grid-column: span 3; }
.grid-span-4 { grid-column: span 4; }
.grid-span-5 { grid-column: span 5; }
.grid-span-6 { grid-column: span 6; }
.grid-span-full { grid-column: 1 / -1; }

/* Responsive grid */
.grid-responsive.grid-cols-2 {
  grid-template-columns: 1fr;
}

.grid-responsive.grid-cols-3 {
  grid-template-columns: 1fr;
}

.grid-responsive.grid-cols-4 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-responsive.grid-cols-5 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-responsive.grid-cols-6 {
  grid-template-columns: repeat(3, 1fr);
}

/* Tablet responsive */
@media (min-width: 768px) {
  .grid-responsive.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-responsive.grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-responsive.grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-responsive.grid-cols-5 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .grid-responsive.grid-cols-6 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Desktop responsive */
@media (min-width: 1024px) {
  .grid-responsive.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-responsive.grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .grid-responsive.grid-cols-5 {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .grid-responsive.grid-cols-6 {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* Mobile adjustments */
@media (max-width: 767px) {
  .grid-responsive {
    grid-template-columns: 1fr !important;
  }
  
  .grid-responsive.grid-cols-4,
  .grid-responsive.grid-cols-5,
  .grid-responsive.grid-cols-6 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .grid-gap-medium {
    gap: 1rem;
  }
  
  .grid-gap-large {
    gap: 1.5rem;
  }
  
  .grid-gap-xl {
    gap: 2rem;
  }
}
