import React from 'react';

const HamburgerButton = ({ isOpen, onClick, className = '' }) => {
  return (
    <button 
      className={`header-mobile-button ${isOpen ? 'open' : ''} ${className}`}
      onClick={onClick}
      aria-label="Toggle menu"
    >
      <div className="hamburger">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </button>
  );
};

export default HamburgerButton;
