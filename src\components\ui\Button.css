.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  font-family: inherit;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):hover {
  transform: translateY(-2px);
}

.btn:not(:disabled):active {
  transform: translateY(0);
}

/* Variants */
.btn-primary {
  background-color: var(--primary-600);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-700);
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.btn-primary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.5);
}

.btn-secondary {
  background-color: transparent;
  border: 2px solid var(--primary-600);
  color: var(--primary-400);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--primary-600);
  color: white;
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.btn-secondary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.5);
}

.btn-ghost {
  background-color: transparent;
  color: var(--secondary-300);
  border: 1px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: rgba(37, 99, 235, 0.1);
  color: var(--primary-400);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--secondary-600);
  color: var(--secondary-300);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--secondary-700);
  border-color: var(--secondary-500);
}

/* Sizes */
.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-medium {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-xl {
  padding: 1.25rem 2.5rem;
  font-size: 1.25rem;
}

/* Icon positioning */
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon-left {
  margin-right: 0.25rem;
}

.btn-icon-right {
  margin-left: 0.25rem;
}

/* Full width */
.btn-full {
  width: 100%;
}

/* Loading state */
.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile responsive */
@media (max-width: 767px) {
  .btn-medium {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
  
  .btn-large {
    padding: 1.25rem 2rem;
    font-size: 1.125rem;
  }
}
