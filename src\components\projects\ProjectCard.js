import React from 'react';
import { Card } from '../ui';
import ProjectLinks from './ProjectLinks';
import TechTag from './TechTag';

const ProjectCard = ({ project, className = '' }) => {
  const cardClasses = `project-card ${project.featured ? 'featured' : ''} ${className}`;

  return (
    <Card 
      hover
      padding="none"
      className={cardClasses}
    >
      {/* Project Image */}
      <div className="project-image">
        <img
          src={project.image}
          alt={project.title}
        />
        <div className="project-overlay">
          <ProjectLinks 
            liveUrl={project.liveUrl}
            githubUrl={project.githubUrl}
          />
        </div>
      </div>

      {/* Project Content */}
      <Card.Body>
        <h3 className="project-title">{project.title}</h3>
        <p className="project-description">{project.description}</p>
        
        {/* Technologies */}
        <div className="project-tech">
          {project.technologies.map((tech, index) => (
            <TechTag key={index} tech={tech} />
          ))}
        </div>
      </Card.Body>
    </Card>
  );
};

export default ProjectCard;
