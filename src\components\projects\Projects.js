import React from 'react';
import { Section, Button } from '../ui';
import ProjectGrid from './ProjectGrid';
import { projects } from '../../data/portfolio';
import './Projects.css';

const Projects = () => {
  const titleWithGradient = (
    <>
      Featured <span className="gradient-text">Projects</span>
    </>
  );

  const description = "Here are some of my recent projects that showcase my skills and passion for creating innovative digital solutions.";

  return (
    <Section 
      id="projects" 
      padding="large"
    >
      <Section.Header 
        title={titleWithGradient}
        description={description}
      />

      <Section.Content>
        <ProjectGrid projects={projects} />
        
        <div className="section-cta">
          <Button 
            variant="secondary"
            icon="🔍"
          >
            View All Projects
          </Button>
        </div>
      </Section.Content>
    </Section>
  );
};

export default Projects;
