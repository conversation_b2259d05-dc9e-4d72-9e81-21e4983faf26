import React from 'react';
import { Button, Grid } from '../ui';
import StatCard from './StatCard';

const AboutContent = ({ 
  title,
  description, 
  stats, 
  onCtaClick,
  ctaText = "Let's Work Together"
}) => {
  return (
    <div className="about-text">
      <h2 className="section-title font-display">
        {title}
      </h2>
      
      <div className="about-description">
        {description.map((paragraph, index) => (
          <p key={index}>{paragraph}</p>
        ))}
      </div>
      
      <Grid cols={2} gap="medium" className="about-stats">
        {stats.map((stat, index) => (
          <StatCard 
            key={index}
            number={stat.number}
            label={stat.label}
          />
        ))}
      </Grid>
      
      <Button onClick={onCtaClick}>
        {ctaText}
      </Button>
    </div>
  );
};

export default AboutContent;
