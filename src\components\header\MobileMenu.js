import React from 'react';
import { Button } from '../ui';

const MobileMenu = ({ 
  isOpen, 
  items, 
  onItemClick, 
  ctaText = "Get In Touch", 
  onCtaClick 
}) => {
  return (
    <div className={`header-mobile-nav ${isOpen ? 'open' : ''}`}>
      <div className="header-mobile-nav-content">
        {items.map((item) => (
          <button
            key={item.name}
            onClick={() => onItemClick(item.href)}
            className="header-mobile-nav-button"
          >
            {item.name}
          </button>
        ))}
        <Button 
          onClick={onCtaClick}
          className="header-mobile-cta"
        >
          {ctaText}
        </Button>
      </div>
    </div>
  );
};

export default MobileMenu;
