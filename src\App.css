/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  transition: all 0.3s ease;
}

.header.scrolled {
  background-color: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(12px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

@media (min-width: 768px) {
  .header-nav {
    height: 5rem;
  }
}

.header-logo {
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
}

@media (min-width: 768px) {
  .header-logo {
    font-size: 1.5rem;
  }
}

.header-desktop-nav {
  display: none;
  align-items: center;
  gap: 2rem;
}

@media (min-width: 768px) {
  .header-desktop-nav {
    display: flex;
  }
}

.header-nav-button {
  background: none;
  border: none;
  color: var(--secondary-300);
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.header-nav-button:hover {
  color: var(--primary-400);
}

.header-mobile-button {
  display: block;
  background: none;
  border: none;
  color: var(--secondary-300);
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: 1.5rem;
}

@media (min-width: 768px) {
  .header-mobile-button {
    display: none;
  }
}

.header-mobile-button:hover {
  color: var(--primary-400);
}

.header-mobile-nav {
  background-color: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(12px);
}

@media (min-width: 768px) {
  .header-mobile-nav {
    display: none;
  }
}

.header-mobile-nav-content {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-mobile-nav-button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  color: var(--secondary-300);
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.header-mobile-nav-button:hover {
  color: var(--primary-400);
}

.header-mobile-cta {
  margin: 0 1rem;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 4rem 0;
}

.hero-background {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.hero-bg-element {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
}

.hero-bg-1 {
  top: -10rem;
  right: -10rem;
  width: 20rem;
  height: 20rem;
  background: rgba(37, 99, 235, 0.2);
}

.hero-bg-2 {
  bottom: -10rem;
  left: -10rem;
  width: 20rem;
  height: 20rem;
  background: rgba(168, 85, 247, 0.2);
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
}

.hero-greeting {
  color: var(--primary-400);
  font-weight: 500;
  font-size: 1.125rem;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .hero-greeting {
    font-size: 1.25rem;
  }
}

.hero-name {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .hero-name {
    font-size: 3.75rem;
  }
}

@media (min-width: 1024px) {
  .hero-name {
    font-size: 4.5rem;
  }
}

.hero-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--secondary-300);
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 2.25rem;
  }
}

.hero-description {
  font-size: 1.125rem;
  color: var(--secondary-400);
  max-width: 48rem;
  margin: 0 auto 3rem;
  line-height: 1.7;
}

@media (min-width: 768px) {
  .hero-description {
    font-size: 1.25rem;
  }
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  margin-bottom: 4rem;
}

@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
  }
}

.hero-social {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.social-link {
  color: var(--secondary-400);
  text-decoration: none;
  transition: color 0.3s ease, transform 0.3s ease;
  font-weight: 500;
}

.social-link:hover {
  color: var(--primary-400);
  transform: translateY(-2px);
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
}

.scroll-mouse {
  width: 1.5rem;
  height: 2.5rem;
  border: 2px solid var(--secondary-400);
  border-radius: 1rem;
  display: flex;
  justify-content: center;
  position: relative;
}

.scroll-wheel {
  width: 0.25rem;
  height: 0.75rem;
  background: var(--primary-400);
  border-radius: 0.125rem;
  margin-top: 0.5rem;
  animation: scroll-bounce 2s infinite;
}

@keyframes scroll-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(0.75rem);
  }
  60% {
    transform: translateY(0.375rem);
  }
}

/* About Section */
.about-section {
  padding: 4rem 0;
  background-color: rgba(30, 41, 59, 0.5);
}

@media (min-width: 768px) {
  .about-section {
    padding: 6rem 0;
  }
}

.about-grid {
  display: grid;
  gap: 3rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .about-grid {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

.section-title {
  font-size: 1.875rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .section-title {
    font-size: 2.25rem;
  }
}

@media (min-width: 1024px) {
  .section-title {
    font-size: 3rem;
  }
}

.about-description {
  color: var(--secondary-300);
  font-size: 1.125rem;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.about-description p {
  margin-bottom: 1rem;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
  .stat-number {
    font-size: 1.875rem;
  }
}

.stat-label {
  color: var(--secondary-400);
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .stat-label {
    font-size: 1rem;
  }
}

.skills-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
}

@media (min-width: 768px) {
  .skills-title {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .skills-title {
    text-align: left;
  }
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .skills-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .skills-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.skill-item:hover {
  transform: translateY(-5px) scale(1.1);
}

.skill-icon {
  width: 4rem;
  height: 4rem;
  background-color: var(--secondary-700);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  font-size: 2rem;
  transition: background-color 0.3s ease;
}

.skill-item:hover .skill-icon {
  background-color: var(--secondary-600);
}

.skill-item span {
  font-size: 0.875rem;
  color: var(--secondary-400);
  text-align: center;
  transition: color 0.3s ease;
}

.skill-item:hover span {
  color: var(--secondary-300);
}

/* Responsive Design */
@media (max-width: 767px) {
  .container-custom {
    padding: 0 1rem;
  }

  .hero-section {
    padding: 6rem 0 4rem;
  }

  .hero-name {
    font-size: 2rem;
  }

  .hero-title {
    font-size: 1.125rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-buttons {
    width: 100%;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    justify-content: center;
  }

  .about-grid {
    gap: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .skill-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
  }
}

/* Projects Section */
.projects-section {
  padding: 4rem 0;
}

@media (min-width: 768px) {
  .projects-section {
    padding: 6rem 0;
  }
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-description {
  font-size: 1.125rem;
  color: var(--secondary-400);
  max-width: 48rem;
  margin: 0 auto;
  line-height: 1.7;
}

@media (min-width: 768px) {
  .section-description {
    font-size: 1.25rem;
  }
}

.projects-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
}

@media (min-width: 768px) {
  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .projects-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.project-card {
  background-color: var(--secondary-800);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.project-card.featured {
  grid-column: span 1;
}

@media (min-width: 768px) {
  .project-card.featured {
    grid-column: span 2;
  }
}

.project-image {
  position: relative;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 12rem;
  object-fit: cover;
  transition: transform 0.5s ease;
}

@media (min-width: 768px) {
  .project-image img {
    height: 16rem;
  }
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(15, 23, 42, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 1rem;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-links {
  display: flex;
  gap: 0.5rem;
}

.project-link {
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--primary-600);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 1.125rem;
  transition: all 0.3s ease;
}

.project-link:hover {
  background-color: var(--primary-700);
  transform: scale(1.1);
}

.project-content {
  padding: 1.5rem;
}

.project-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  transition: color 0.3s ease;
}

@media (min-width: 768px) {
  .project-title {
    font-size: 1.5rem;
  }
}

.project-card:hover .project-title {
  color: var(--primary-400);
}

.project-description {
  color: var(--secondary-400);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  padding: 0.25rem 0.75rem;
  background-color: rgba(37, 99, 235, 0.2);
  color: var(--primary-400);
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.section-cta {
  text-align: center;
}

/* Contact Section */
.contact-section {
  padding: 4rem 0;
  background-color: rgba(30, 41, 59, 0.5);
}

@media (min-width: 768px) {
  .contact-section {
    padding: 6rem 0;
  }
}

.contact-grid {
  display: grid;
  gap: 3rem;
}

@media (min-width: 1024px) {
  .contact-grid {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

.contact-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .contact-title {
    font-size: 1.875rem;
  }
}

.contact-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: rgba(51, 65, 85, 0.5);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.contact-item:hover {
  background-color: var(--secondary-700);
  transform: translateX(10px);
}

.contact-icon {
  width: 3rem;
  height: 3rem;
  background-color: var(--primary-600);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: background-color 0.3s ease;
}

.contact-item:hover .contact-icon {
  background-color: var(--primary-700);
}

.contact-details h4 {
  font-weight: 600;
  color: var(--secondary-200);
  margin-bottom: 0.25rem;
}

.contact-details p {
  color: var(--secondary-400);
  margin: 0;
  transition: color 0.3s ease;
}

.contact-item:hover .contact-details p {
  color: var(--secondary-300);
}

.contact-social h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-icon {
  width: 3rem;
  height: 3rem;
  background-color: var(--secondary-700);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: var(--secondary-400);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background-color: var(--secondary-600);
  color: var(--primary-400);
  transform: translateY(-2px);
}

/* Contact Form */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .form-row {
    grid-template-columns: 1fr 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--secondary-300);
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--secondary-700);
  border: 1px solid var(--secondary-600);
  border-radius: 0.5rem;
  color: var(--secondary-200);
  font-size: 1rem;
  transition: all 0.3s ease;
  resize: none;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--secondary-400);
}

.form-submit {
  width: 100%;
  justify-content: center;
}

/* Footer */
.footer {
  background-color: var(--secondary-900);
  border-top: 1px solid var(--secondary-800);
  padding: 2rem 0;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

@media (min-width: 768px) {
  .footer-content {
    flex-direction: row;
  }
}

.footer-text {
  color: var(--secondary-400);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.back-to-top {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--secondary-400);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.back-to-top:hover {
  color: var(--primary-400);
  transform: translateY(-2px);
}

.back-to-top-icon {
  width: 2rem;
  height: 2rem;
  background-color: var(--secondary-800);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-to-top:hover .back-to-top-icon {
  background-color: var(--primary-600);
  color: white;
}
