import React, { useState, useEffect } from 'react';
import Logo from './Logo';
import Navigation from './Navigation';
import HamburgerButton from './HamburgerButton';
import MobileMenu from './MobileMenu';
import MobileBackdrop from './MobileBackdrop';
import { navigationItems } from '../../data/portfolio';
import './Header.css';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      setScrolled(isScrolled);
    };

    const handleClickOutside = (event) => {
      if (isMenuOpen && !event.target.closest('.header-nav')) {
        setIsMenuOpen(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isMenuOpen]);

  const scrollToSection = (href) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  const handleCtaClick = () => {
    scrollToSection('#contact');
  };

  const handleLogoClick = () => {
    scrollToSection('#home');
  };

  return (
    <>
      <header className={`header ${scrolled ? 'scrolled' : ''}`}>
        <nav className="container-custom">
          <div className="header-nav">
            <Logo 
              onClick={handleLogoClick}
            />
            
            <Navigation 
              items={navigationItems}
              onItemClick={scrollToSection}
              onCtaClick={handleCtaClick}
            />
            
            <HamburgerButton 
              isOpen={isMenuOpen}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            />
            
            <MobileMenu 
              isOpen={isMenuOpen}
              items={navigationItems}
              onItemClick={scrollToSection}
              onCtaClick={handleCtaClick}
            />
          </div>
        </nav>
      </header>
      
      <MobileBackdrop 
        isVisible={isMenuOpen}
        onClick={() => setIsMenuOpen(false)}
      />
    </>
  );
};

export default Header;
