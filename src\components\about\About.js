import React from 'react';
import { Section, Grid } from '../ui';
import AboutContent from './AboutContent';
import SkillsGrid from './SkillsGrid';
import { aboutInfo, stats, skills } from '../../data/portfolio';
import './About.css';

const About = () => {
  const scrollToSection = (href) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleCtaClick = () => {
    scrollToSection('#contact');
  };

  const titleWithGradient = (
    <>
      About <span className="gradient-text">Me</span>
    </>
  );

  const skillsTitleWithGradient = (
    <>
      Skills & <span className="gradient-text">Technologies</span>
    </>
  );

  return (
    <Section 
      id="about" 
      variant="accent"
      padding="large"
    >
      <Grid cols={2} gap="large" className="about-grid">
        <AboutContent 
          title={titleWithGradient}
          description={aboutInfo.description}
          stats={stats}
          onCtaClick={handleCtaClick}
        />
        
        <SkillsGrid 
          title={skillsTitleWithGradient}
          skills={skills}
        />
      </Grid>
    </Section>
  );
};

export default About;
