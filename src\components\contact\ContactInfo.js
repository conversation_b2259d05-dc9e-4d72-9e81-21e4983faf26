import React from 'react';
import ContactItem from './ContactItem';
import SocialIcons from './SocialIcons';

const ContactInfo = ({ 
  title, 
  items, 
  socialLinks, 
  socialTitle,
  className = '' 
}) => {
  return (
    <div className={`contact-info ${className}`}>
      <h3 className="contact-title font-display">
        {title}
      </h3>

      <div className="contact-items">
        {items.map((item, index) => (
          <ContactItem 
            key={index}
            item={item}
          />
        ))}
      </div>

      <SocialIcons 
        links={socialLinks}
        title={socialTitle}
      />
    </div>
  );
};

export default ContactInfo;
