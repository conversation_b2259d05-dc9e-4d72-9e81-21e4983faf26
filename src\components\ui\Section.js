import React from 'react';
import './Section.css';

const Section = ({ 
  children, 
  id,
  variant = 'default',
  padding = 'medium',
  className = '',
  ...props 
}) => {
  const baseClasses = `section section-${variant} section-padding-${padding}`;
  const classes = `${baseClasses} ${className}`.trim();

  return (
    <section 
      id={id}
      className={classes}
      {...props}
    >
      <div className="container-custom">
        {children}
      </div>
    </section>
  );
};

const SectionHeader = ({ 
  title, 
  subtitle, 
  description,
  centered = true,
  className = '',
  ...props 
}) => {
  const classes = `section-header ${centered ? 'section-header-centered' : ''} ${className}`.trim();

  return (
    <div className={classes} {...props}>
      {subtitle && <div className="section-subtitle">{subtitle}</div>}
      {title && <h2 className="section-title font-display">{title}</h2>}
      {description && <p className="section-description">{description}</p>}
    </div>
  );
};

const SectionContent = ({ children, className = '', ...props }) => (
  <div className={`section-content ${className}`} {...props}>
    {children}
  </div>
);

Section.Header = SectionHeader;
Section.Content = SectionContent;

export default Section;
