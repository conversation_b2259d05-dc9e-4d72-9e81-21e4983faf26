/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 4rem 0;
}

.hero-background {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.hero-bg-element {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
}

.hero-bg-1 {
  top: -10rem;
  right: -10rem;
  width: 20rem;
  height: 20rem;
  background: rgba(37, 99, 235, 0.2);
}

.hero-bg-2 {
  bottom: -10rem;
  left: -10rem;
  width: 20rem;
  height: 20rem;
  background: rgba(168, 85, 247, 0.2);
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  width: 100%;
}

.hero-greeting {
  color: var(--primary-400);
  font-weight: 500;
  font-size: 1.125rem;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .hero-greeting {
    font-size: 1.25rem;
  }
}

.hero-name {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

@media (min-width: 768px) {
  .hero-name {
    font-size: 3.75rem;
  }
}

@media (min-width: 1024px) {
  .hero-name {
    font-size: 4.5rem;
  }
}

.hero-title {
  font-size: 1.375rem;
  font-weight: 600;
  color: var(--secondary-300);
  margin-bottom: 2rem;
  line-height: 1.3;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 1.875rem;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 2.25rem;
  }
}

.hero-description {
  font-size: 1.125rem;
  color: var(--secondary-400);
  max-width: 48rem;
  margin: 0 auto 2.5rem;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .hero-description {
    font-size: 1.25rem;
    margin-bottom: 3rem;
  }
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  margin-bottom: 3rem;
}

@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
  }
}

.hero-social {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 2rem;
}

.social-link {
  color: var(--secondary-400);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 1rem;
}

.social-link:hover {
  color: var(--primary-400);
  transform: translateY(-2px);
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
}

.scroll-mouse {
  width: 1.5rem;
  height: 2.5rem;
  border: 2px solid var(--secondary-400);
  border-radius: 1rem;
  display: flex;
  justify-content: center;
  position: relative;
}

.scroll-wheel {
  width: 0.25rem;
  height: 0.75rem;
  background: var(--primary-400);
  border-radius: 0.125rem;
  margin-top: 0.5rem;
  animation: scroll-bounce 2s infinite;
}

@keyframes scroll-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(0.75rem);
  }
  60% {
    transform: translateY(0.375rem);
  }
}

/* Mobile responsive */
@media (max-width: 767px) {
  .hero-section {
    padding: 6rem 1rem 4rem;
    min-height: 100vh;
    display: flex;
    align-items: center;
  }
  
  .hero-name {
    font-size: 2.5rem;
    line-height: 1.1;
    margin-bottom: 1.5rem;
  }
  
  .hero-title {
    font-size: 1.375rem;
    margin-bottom: 2rem;
    line-height: 1.3;
  }
  
  .hero-description {
    font-size: 1.125rem;
    margin-bottom: 2.5rem;
    padding: 0;
    line-height: 1.6;
  }
  
  .hero-buttons {
    width: 100%;
    gap: 1rem;
    margin-bottom: 3rem;
  }
  
  .hero-social {
    gap: 2rem;
    margin-top: 2rem;
  }
  
  .social-link {
    font-size: 1rem;
    font-weight: 500;
  }
  
  .scroll-indicator {
    display: none;
  }
}
