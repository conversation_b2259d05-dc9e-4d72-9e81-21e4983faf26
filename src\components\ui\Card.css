.card {
  background-color: var(--secondary-800);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.card-default {
  border: 1px solid var(--secondary-700);
}

.card-elevated {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-outlined {
  border: 2px solid var(--secondary-600);
  background-color: transparent;
}

.card-glass {
  background-color: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(203, 213, 225, 0.1);
}

/* Hover effects */
.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.card-clickable {
  cursor: pointer;
}

.card-clickable:hover {
  transform: translateY(-2px);
}

/* Padding variants */
.card-padding-none {
  padding: 0;
}

.card-padding-small {
  padding: 1rem;
}

.card-padding-medium {
  padding: 1.5rem;
}

.card-padding-large {
  padding: 2rem;
}

/* Card components */
.card-header {
  padding: 1.5rem 1.5rem 0;
  border-bottom: 1px solid var(--secondary-700);
  margin-bottom: 1.5rem;
}

.card-body {
  padding: 0 1.5rem;
  flex: 1;
}

.card-footer {
  padding: 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid var(--secondary-700);
  margin-top: 1.5rem;
}

.card-image {
  position: relative;
  overflow: hidden;
  margin: -1.5rem -1.5rem 1.5rem;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.card:hover .card-image img {
  transform: scale(1.05);
}

/* When card has no padding, adjust image margins */
.card-padding-none .card-image {
  margin: 0;
}

.card-padding-none .card-header {
  padding: 1.5rem;
  margin-bottom: 0;
}

.card-padding-none .card-body {
  padding: 1.5rem;
}

.card-padding-none .card-footer {
  padding: 1.5rem;
  margin-top: 0;
}

/* Responsive */
@media (max-width: 767px) {
  .card-padding-medium {
    padding: 1rem;
  }
  
  .card-padding-large {
    padding: 1.5rem;
  }
  
  .card-header,
  .card-body,
  .card-footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-image {
    margin-left: -1rem;
    margin-right: -1rem;
  }
}
