import React from 'react';
import { Grid } from '../ui';
import ProjectCard from './ProjectCard';

const ProjectGrid = ({ projects, className = '' }) => {
  return (
    <Grid cols={3} gap="large" className={`projects-grid ${className}`}>
      {projects.map((project) => (
        <ProjectCard 
          key={project.id} 
          project={project}
        />
      ))}
    </Grid>
  );
};

export default ProjectGrid;
