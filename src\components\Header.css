.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  transition: all 0.3s ease;
}

.header.scrolled {
  background-color: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(12px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

@media (min-width: 768px) {
  .header-nav {
    height: 5rem;
  }
}

.header-logo {
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
}

@media (min-width: 768px) {
  .header-logo {
    font-size: 1.5rem;
  }
}

.header-desktop-nav {
  display: none;
  align-items: center;
  gap: 2rem;
}

@media (min-width: 768px) {
  .header-desktop-nav {
    display: flex;
  }
}

.header-nav-button {
  background: none;
  border: none;
  color: var(--secondary-300);
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.header-nav-button:hover {
  color: var(--primary-400);
}

.header-mobile-button {
  display: block;
  background: none;
  border: none;
  color: var(--secondary-300);
  cursor: pointer;
  transition: color 0.3s ease;
}

@media (min-width: 768px) {
  .header-mobile-button {
    display: none;
  }
}

.header-mobile-button:hover {
  color: var(--primary-400);
}

.header-mobile-nav {
  overflow: hidden;
  background-color: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(12px);
}

@media (min-width: 768px) {
  .header-mobile-nav {
    display: none;
  }
}

.header-mobile-nav-content {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-mobile-nav-button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  color: var(--secondary-300);
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: 1rem;
}

.header-mobile-nav-button:hover {
  color: var(--primary-400);
}

.header-mobile-cta {
  margin: 0 1rem;
}
