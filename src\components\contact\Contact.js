import React from 'react';
import { Section, Grid } from '../ui';
import ContactInfo from './ContactInfo';
import ContactForm from './ContactForm';
import { contactInfo, socialLinks } from '../../data/portfolio';
import './Contact.css';

const Contact = () => {
  const handleFormSubmit = (formData) => {
    // Handle form submission here
    console.log('Form submitted:', formData);
    // You can integrate with email service, API, etc.
  };

  const titleWithGradient = (
    <>
      Get In <span className="gradient-text">Touch</span>
    </>
  );

  const subtitleWithGradient = (
    <>
      Let's <span className="gradient-text">Connect</span>
    </>
  );

  return (
    <Section 
      id="contact" 
      variant="accent"
      padding="large"
    >
      <Section.Header 
        title={titleWithGradient}
        description={contactInfo.description}
      />

      <Section.Content>
        <Grid cols={2} gap="large" className="contact-grid">
          <ContactInfo 
            title={subtitleWithGradient}
            items={contactInfo.items}
            socialLinks={socialLinks}
            socialTitle="Follow Me"
          />
          
          <ContactForm 
            onSubmit={handleFormSubmit}
          />
        </Grid>
      </Section.Content>
    </Section>
  );
};

export default Contact;
