import React from 'react';
import { Grid } from '../ui';
import SkillCard from './SkillCard';

const SkillsGrid = ({ title, skills }) => {
  return (
    <div className="about-skills">
      <h3 className="skills-title font-display">
        {title}
      </h3>
      
      <Grid cols={5} gap="medium" className="skills-grid">
        {skills.map((skill, index) => (
          <SkillCard 
            key={index}
            skill={skill}
          />
        ))}
      </Grid>
    </div>
  );
};

export default SkillsGrid;
