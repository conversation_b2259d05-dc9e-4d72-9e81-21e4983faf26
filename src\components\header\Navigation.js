import React from 'react';
import { Button } from '../ui';

const Navigation = ({ items, onItemClick, ctaText = "Get In Touch", onCtaClick }) => {
  return (
    <div className="header-desktop-nav">
      {items.map((item) => (
        <button
          key={item.name}
          onClick={() => onItemClick(item.href)}
          className="header-nav-button"
        >
          {item.name}
        </button>
      ))}
      <Button onClick={onCtaClick}>
        {ctaText}
      </Button>
    </div>
  );
};

export default Navigation;
