.section {
  width: 100%;
  position: relative;
}

.section-default {
  background-color: transparent;
}

.section-primary {
  background-color: var(--primary-900);
}

.section-secondary {
  background-color: var(--secondary-800);
}

.section-accent {
  background-color: rgba(30, 41, 59, 0.5);
}

.section-gradient {
  background: linear-gradient(135deg, var(--secondary-900) 0%, var(--secondary-800) 100%);
}

/* Padding variants */
.section-padding-none {
  padding: 0;
}

.section-padding-small {
  padding: 2rem 0;
}

.section-padding-medium {
  padding: 4rem 0;
}

.section-padding-large {
  padding: 6rem 0;
}

.section-padding-xl {
  padding: 8rem 0;
}

/* Section header */
.section-header {
  margin-bottom: 4rem;
}

.section-header-centered {
  text-align: center;
}

.section-subtitle {
  color: var(--primary-400);
  font-weight: 500;
  font-size: 1rem;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.section-title {
  font-size: 1.875rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.section-description {
  font-size: 1.125rem;
  color: var(--secondary-400);
  line-height: 1.7;
  max-width: 48rem;
}

.section-header-centered .section-description {
  margin: 0 auto;
}

/* Section content */
.section-content {
  position: relative;
}

/* Responsive */
@media (min-width: 768px) {
  .section-padding-medium {
    padding: 6rem 0;
  }
  
  .section-padding-large {
    padding: 8rem 0;
  }
  
  .section-padding-xl {
    padding: 10rem 0;
  }
  
  .section-title {
    font-size: 2.25rem;
  }
  
  .section-description {
    font-size: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .section-title {
    font-size: 3rem;
  }
}

@media (max-width: 767px) {
  .section-padding-medium {
    padding: 3rem 0;
  }
  
  .section-padding-large {
    padding: 4rem 0;
  }
  
  .section-padding-xl {
    padding: 5rem 0;
  }
  
  .section-header {
    margin-bottom: 2.5rem;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
  
  .section-description {
    font-size: 1rem;
  }
}
