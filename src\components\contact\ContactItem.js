import React from 'react';

const ContactItem = ({ item, className = '' }) => {
  const Component = item.href ? 'a' : 'div';
  const props = item.href ? { href: item.href } : {};

  return (
    <Component 
      className={`contact-item ${className}`}
      {...props}
    >
      <div className="contact-icon">
        {item.icon}
      </div>
      <div className="contact-details">
        <h4>{item.title}</h4>
        <p>{item.value}</p>
      </div>
    </Component>
  );
};

export default ContactItem;
